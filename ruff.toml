target-version = "py312"
line-length = 80
fix = true
show-fixes = true
exclude = [
    ".eggs/",
    ".git/",
    ".mypy_cache/",
    ".conda/",
    ".venv/",
    ".tox/",
    ".vscode/",
    ".pytest_cache/",
    ".test/",
    "dist/",
]


[format]
preview = true
docstring-code-line-length = 100
docstring-code-format = true

[lint]
select = ["ALL"]
ignore = [
    "ANN003", # Missing type annotation for kwargs
    "CPY", # No copyright header
    "D100", # Missing docstring in public module
    "D101", # Missing docstring in public class
    "D102", # Missing docstring in public method
    "D103", # Missing docstring in public function
    "D104", # Missing docstring in public package
    "D105", # Missing docstring in magic method
    "D106", # Missing docstring in public nested class
    "D107", # Missing docstring in __init__
    "D203", # 1 blank line required before class docstring
    "D212", # Multi-line docstring summary should start at the first line
    "N802", # Function name should be lowercase
    "N803", # Argument name should be lowercase
    "N806", # Variable in function should be lowercase
    "TID252",
    "EXE",
    "COM812",
    "ISC001",
]

preview = true

[lint.pylint]
max-args = 15
max-public-methods = 30

[lint.per-file-ignores]
"tests/**/*.py" = [
    "INP001", # No implicit namespace package
    "PLR0913", # Too many arguments.
    "PLR0917", # Too many positional arguments.
    "PLR2004", # Magic value comparison
    "S101", # Use of assert detected
]
"examples/**/*.py" = [
    "INP001", # No implicit namespace package
    "PLR0913", # Too many arguments.
    "PLR0917", # Too many positional arguments.
    "PLR2004", # Magic value comparison
    "S101", # Use of assert detected
    "T",
]

[lint.isort]
known-first-party = ["ocean", "tests"]

[lint.flake8-builtins]
builtins-allowed-modules = ["types"]
