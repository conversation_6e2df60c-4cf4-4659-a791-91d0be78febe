from anytree import NodeMixin

from ..typing import Array, Key, NonNegativeInt, NonNegativeNumber, PositiveInt

class Node(NodeMixin):
    def __init__(
        self,
        node_id: NonNegativeInt,
        *,
        feature: Key | None = None,
        value: Array | None = None,
        parent: Node | None = None,
        threshold: float | None = None,
        code: Key | None = None,
        n_samples: NonNegativeInt = 0,
        left: Node | None = None,
        right: Node | None = None,
    ) -> None: ...
    @property
    def node_id(self) -> NonNegativeInt: ...
    @property
    def feature(self) -> Key: ...
    @property
    def value(self) -> Array: ...
    @property
    def threshold(self) -> float: ...
    @property
    def code(self) -> Key: ...
    @property
    def n_samples(self) -> NonNegativeInt: ...
    @property
    def length(self) -> NonNegativeNumber: ...
    @property
    def is_leaf(self) -> bool: ...
    @property
    def size(self) -> PositiveInt: ...
    @property
    def height(self) -> NonNegativeInt: ...
    @property
    def depth(self) -> NonNegativeInt: ...
    @property
    def parent(self) -> Node | None: ...
    @parent.setter
    def parent(self, value: Node | None) -> None: ...
    @property
    def children(self) -> tuple[Node, ...]: ...
    @children.setter
    def children(self, children: tuple[Node, ...]) -> None: ...
    @children.deleter
    def children(self) -> None: ...
    @property
    def leaves(self) -> tuple[Node, *tuple[Node, ...]]: ...
    @property
    def left(self) -> Node: ...
    @left.setter
    def left(self, node: Node) -> None: ...
    @property
    def right(self) -> Node: ...
    @right.setter
    def right(self, node: Node) -> None: ...
    @property
    def sigma(self) -> bool: ...
