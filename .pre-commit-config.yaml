# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
        args: ['--maxkb=100']

# Run ruff:
# See https://github.com/astral-sh/ruff-pre-commit for more information
-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.9.4
    hooks:
    -   id: ruff
        args: [--fix]
    -   id: ruff-format

# Run mypy:
# See https://github.com/pre-commit/mirrors-mypy
-   repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.14.1
    hooks:
    -   id: mypy
        name: mypy
        language: python
        entry: mypy
        args: [--config-file=mypy.ini, ocean, tests]
        pass_filenames: false
        additional_dependencies: [
            anytree,
            gurobipy,
            numpy,
            ortools,
            pandas,
            pandas-stubs,
            pydantic,
            pytest,
            scikit-learn,
            scipy,
            scipy-stubs,
        ]

# Run pyright:
# See: https://github.com/RobertCraigie/pyright-python
-   repo: https://github.com/RobertCraigie/pyright-python
    rev: v1.1.393
    hooks:
    -   id: pyright
        name: pyright
        args: [ocean, tests]
        additional_dependencies: [
            anytree,
            gurobipy,
            numpy,
            ortools,
            pandas,
            pandas-stubs,
            pydantic,
            pytest,
            rich,
            scikit-learn,
            scipy,
            scipy-stubs,
        ]
