name: Release and Publish to Py<PERSON>

on:
  push:
    tags:
      - "v*"

permissions:
  contents: write

jobs:
  tests:
    uses: ./.github/workflows/test.yml

  release:
    name: Release new version
    needs: tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          tag: ${{ github.ref_name }}
        run: |
          gh release create "$tag" \
            --repo="$GITHUB_REPOSITORY" \
            --title="${GITHUB_REPOSITORY#*/} ${tag#v}" \
            --generate-notes
  publish:
    name: publish
    needs: release
    runs-on: ubuntu-latest
    steps:
    - name: Checkout source
      uses: actions/checkout@v4
    - name: Set up Python 3.12
      uses: actions/setup-python@v5
      with:
        python-version: "3.12"
    - name: Build package
      run: |
        python -m pip install -U pip build
        python -m build
    - name: Publish
      uses: pypa/gh-action-pypi-publish@v1.5.0
      with:
        user: __token__
        password: ${{ secrets.PYPI_TOKEN }}
