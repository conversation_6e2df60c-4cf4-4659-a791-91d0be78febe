name: test

on:
  push:
    branches:
      - main
    paths-ignore:
      - "**/*.md"
      - "**/*.yml"
      - "**/LICENSE"
  pull_request:
  workflow_call:

permissions:
  contents: read

jobs:
  build:
    strategy:
      matrix:
        python-version: ["3.12"]
        os: [ubuntu-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install tox
      - name: Run tests with tox
        run: tox
